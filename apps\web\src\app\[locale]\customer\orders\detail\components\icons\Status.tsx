const OrderPending = () => {
  return (
    <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M23 21.5V3.5H1V21.5H23ZM21 19.5H3V10H21V19.5ZM21 8V5.5H3V8H21ZM9.5 16V14H5V16H9.5Z"
        fill="#DA291C"
      />
    </svg>
  )
}

const OrderProcessing = () => {
  return (
    <svg width="28" height="29" viewBox="0 0 28 29" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.06842 2.83203L2.33301 9.93515V26.1654H25.6663V9.93515L20.9309 2.83203H20.3633H7.06842ZM19.7957 4.95324L22.624 9.19567H5.37537L8.20366 4.95324H19.7957ZM4.45422 11.3169H23.5451V24.0442H4.45422V11.3169Z"
        fill="#DA291C"
      />
      <path
        d="M19.4961 18.0908V19.7542H8.50391V17.4208H15.5263L14.3804 16.2749L14.4086 14.6532L16.0303 14.625L19.4961 18.0908Z"
        fill="#DA291C"
      />
    </svg>
  )
}

const OrderShipped = () => {
  return (
    <svg width="28" height="29" viewBox="0 0 28 29" fill="none" xmlns="http://www.w3.org/2000/svg">
      <mask id="path-1-inside-1_38925_76785" fill="white">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M20.5035 4H19.5572H2.33496H1.16829V5.16667V21.5V22.6667H2.33496H4.86745C5.34793 24.0261 6.64437 25 8.16829 25C9.69221 25 10.9887 24.0261 11.4691 22.6667H15.3674C15.8479 24.0261 17.1444 25 18.6683 25C20.1922 25 21.4887 24.0261 21.9691 22.6667H25.1127H25.6683H26.835V21.5V12.543V11.7687L26.1214 11.4679L21.6839 9.5975L20.6987 4.92593L20.5035 4ZM11.4691 20.3333C10.9887 18.9739 9.69221 18 8.16829 18C6.64437 18 5.34793 18.9739 4.86745 20.3333H3.50163V6.33333H18.6109L19.5267 10.6762L19.6527 11.2735L20.2151 11.5106L24.5016 13.3173V20.3333H21.9691C21.4887 18.9739 20.1922 18 18.6683 18C17.1444 18 15.8479 18.9739 15.3674 20.3333H11.4691ZM18.6683 22.6667C18.024 22.6667 17.5016 22.1443 17.5016 21.5C17.5016 20.8557 18.024 20.3333 18.6683 20.3333C19.3126 20.3333 19.835 20.8557 19.835 21.5C19.835 22.1443 19.3126 22.6667 18.6683 22.6667ZM7.00163 21.5C7.00163 22.1443 7.52396 22.6667 8.16829 22.6667C8.81263 22.6667 9.33496 22.1443 9.33496 21.5C9.33496 20.8557 8.81263 20.3333 8.16829 20.3333C7.52396 20.3333 7.00163 20.8557 7.00163 21.5Z"
        />
      </mask>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M20.5035 4H19.5572H2.33496H1.16829V5.16667V21.5V22.6667H2.33496H4.86745C5.34793 24.0261 6.64437 25 8.16829 25C9.69221 25 10.9887 24.0261 11.4691 22.6667H15.3674C15.8479 24.0261 17.1444 25 18.6683 25C20.1922 25 21.4887 24.0261 21.9691 22.6667H25.1127H25.6683H26.835V21.5V12.543V11.7687L26.1214 11.4679L21.6839 9.5975L20.6987 4.92593L20.5035 4ZM11.4691 20.3333C10.9887 18.9739 9.69221 18 8.16829 18C6.64437 18 5.34793 18.9739 4.86745 20.3333H3.50163V6.33333H18.6109L19.5267 10.6762L19.6527 11.2735L20.2151 11.5106L24.5016 13.3173V20.3333H21.9691C21.4887 18.9739 20.1922 18 18.6683 18C17.1444 18 15.8479 18.9739 15.3674 20.3333H11.4691ZM18.6683 22.6667C18.024 22.6667 17.5016 22.1443 17.5016 21.5C17.5016 20.8557 18.024 20.3333 18.6683 20.3333C19.3126 20.3333 19.835 20.8557 19.835 21.5C19.835 22.1443 19.3126 22.6667 18.6683 22.6667ZM7.00163 21.5C7.00163 22.1443 7.52396 22.6667 8.16829 22.6667C8.81263 22.6667 9.33496 22.1443 9.33496 21.5C9.33496 20.8557 8.81263 20.3333 8.16829 20.3333C7.52396 20.3333 7.00163 20.8557 7.00163 21.5Z"
        fill="#DA291C"
      />
      <path
        d="M20.5035 4V1.66667H22.3961L22.7866 3.51853L20.5035 4ZM1.16829 4H-1.16504V1.66667H1.16829V4ZM1.16829 22.6667V25H-1.16504V22.6667H1.16829ZM4.86745 22.6667V20.3333H6.51753L7.06741 21.8891L4.86745 22.6667ZM11.4691 22.6667L9.26918 21.8891L9.81906 20.3333H11.4691V22.6667ZM15.3674 22.6667V20.3333H17.0175L17.5674 21.8891L15.3674 22.6667ZM21.9691 22.6667L19.7692 21.8891L20.3191 20.3333H21.9691V22.6667ZM26.835 22.6667H29.1683V25H26.835V22.6667ZM26.835 11.7687L27.7413 9.61856L29.1683 10.2201V11.7687H26.835ZM26.1214 11.4679L27.0277 9.31781L27.0277 9.31781L26.1214 11.4679ZM21.6839 9.5975L20.7776 11.7476L19.6527 11.2735L19.4008 10.079L21.6839 9.5975ZM20.6987 4.92593L18.4156 5.4074V5.4074L20.6987 4.92593ZM11.4691 20.3333V22.6667H9.81906L9.26918 21.1109L11.4691 20.3333ZM4.86745 20.3333L7.06741 21.1109L6.51753 22.6667H4.86745V20.3333ZM3.50163 20.3333V22.6667H1.16829V20.3333H3.50163ZM3.50163 6.33333H1.16829V4H3.50163V6.33333ZM18.6109 6.33333V4H20.5035L20.894 5.85186L18.6109 6.33333ZM19.5267 10.6762L17.2436 11.1577V11.1577L19.5267 10.6762ZM19.6527 11.2735L18.7464 13.4236L17.6215 12.9494L17.3696 11.7549L19.6527 11.2735ZM20.2151 11.5106L19.3089 13.6607L19.3089 13.6607L20.2151 11.5106ZM24.5016 13.3173L25.4079 11.1672L26.835 11.7687V13.3173H24.5016ZM24.5016 20.3333H26.835V22.6667H24.5016V20.3333ZM21.9691 20.3333V22.6667H20.3191L19.7692 21.1109L21.9691 20.3333ZM15.3674 20.3333L17.5674 21.1109L17.0175 22.6667H15.3674V20.3333ZM19.5572 1.66667H20.5035V6.33333H19.5572V1.66667ZM2.33496 1.66667H19.5572V6.33333H2.33496V1.66667ZM1.16829 1.66667H2.33496V6.33333H1.16829V1.66667ZM-1.16504 5.16667V4H3.50163V5.16667H-1.16504ZM-1.16504 21.5V5.16667H3.50163V21.5H-1.16504ZM-1.16504 22.6667V21.5H3.50163V22.6667H-1.16504ZM2.33496 25H1.16829V20.3333H2.33496V25ZM4.86745 25H2.33496V20.3333H4.86745V25ZM8.16829 27.3333C5.62478 27.3333 3.46717 25.7067 2.66749 23.4442L7.06741 21.8891C7.22868 22.3454 7.66396 22.6667 8.16829 22.6667V27.3333ZM13.6691 23.4442C12.8694 25.7067 10.7118 27.3333 8.16829 27.3333V22.6667C8.67262 22.6667 9.10791 22.3454 9.26918 21.8891L13.6691 23.4442ZM15.3674 25H11.4691V20.3333H15.3674V25ZM18.6683 27.3333C16.1248 27.3333 13.9672 25.7067 13.1675 23.4442L17.5674 21.8891C17.7287 22.3454 18.164 22.6667 18.6683 22.6667V27.3333ZM24.1691 23.4442C23.3694 25.7067 21.2118 27.3333 18.6683 27.3333V22.6667C19.1726 22.6667 19.6079 22.3454 19.7692 21.8891L24.1691 23.4442ZM25.1127 25H21.9691V20.3333H25.1127V25ZM25.6683 25H25.1127V20.3333H25.6683V25ZM26.835 25H25.6683V20.3333H26.835V25ZM29.1683 21.5V22.6667H24.5016V21.5H29.1683ZM29.1683 12.543V21.5H24.5016V12.543H29.1683ZM29.1683 11.7687V12.543H24.5016V11.7687H29.1683ZM27.0277 9.31781L27.7413 9.61856L25.9287 13.9188L25.2151 13.6181L27.0277 9.31781ZM22.5902 7.44736L27.0277 9.31781L25.2151 13.6181L20.7776 11.7476L22.5902 7.44736ZM22.9819 4.44446L23.967 9.11602L19.4008 10.079L18.4156 5.4074L22.9819 4.44446ZM22.7866 3.51853L22.9819 4.44446L18.4156 5.4074L18.2204 4.48147L22.7866 3.51853ZM8.16829 15.6667C10.7118 15.6667 12.8694 17.2933 13.6691 19.5558L9.26918 21.1109C9.10791 20.6546 8.67262 20.3333 8.16829 20.3333V15.6667ZM2.66749 19.5558C3.46717 17.2933 5.62478 15.6667 8.16829 15.6667V20.3333C7.66397 20.3333 7.22868 20.6546 7.06741 21.1109L2.66749 19.5558ZM3.50163 18H4.86745V22.6667H3.50163V18ZM5.83496 6.33333V20.3333H1.16829V6.33333H5.83496ZM18.6109 8.66667H3.50163V4H18.6109V8.66667ZM17.2436 11.1577L16.3278 6.81481L20.894 5.85186L21.8099 10.1947L17.2436 11.1577ZM17.3696 11.7549L17.2436 11.1577L21.8099 10.1947L21.9358 10.792L17.3696 11.7549ZM19.3089 13.6607L18.7464 13.4236L20.559 9.12334L21.1214 9.36042L19.3089 13.6607ZM23.5953 15.4675L19.3089 13.6607L21.1214 9.36042L25.4079 11.1672L23.5953 15.4675ZM22.1683 20.3333V13.3173H26.835V20.3333H22.1683ZM21.9691 18H24.5016V22.6667H21.9691V18ZM18.6683 15.6667C21.2118 15.6667 23.3694 17.2933 24.1691 19.5558L19.7692 21.1109C19.6079 20.6546 19.1726 20.3333 18.6683 20.3333V15.6667ZM13.1675 19.5558C13.9672 17.2933 16.1248 15.6667 18.6683 15.6667V20.3333C18.164 20.3333 17.7287 20.6546 17.5674 21.1109L13.1675 19.5558ZM11.4691 18H15.3674V22.6667H11.4691V18ZM19.835 21.5C19.835 20.8557 19.3126 20.3333 18.6683 20.3333V25C16.7353 25 15.1683 23.433 15.1683 21.5H19.835ZM18.6683 22.6667C19.3126 22.6667 19.835 22.1443 19.835 21.5H15.1683C15.1683 19.567 16.7353 18 18.6683 18V22.6667ZM17.5016 21.5C17.5016 22.1443 18.024 22.6667 18.6683 22.6667V18C20.6013 18 22.1683 19.567 22.1683 21.5H17.5016ZM18.6683 20.3333C18.024 20.3333 17.5016 20.8557 17.5016 21.5H22.1683C22.1683 23.433 20.6013 25 18.6683 25V20.3333ZM8.16829 25C6.2353 25 4.66829 23.433 4.66829 21.5H9.33496C9.33496 20.8557 8.81263 20.3333 8.16829 20.3333V25ZM11.6683 21.5C11.6683 23.433 10.1013 25 8.16829 25V20.3333C7.52396 20.3333 7.00163 20.8557 7.00163 21.5H11.6683ZM8.16829 18C10.1013 18 11.6683 19.567 11.6683 21.5H7.00163C7.00163 22.1443 7.52396 22.6667 8.16829 22.6667V18ZM4.66829 21.5C4.66829 19.567 6.23529 18 8.16829 18V22.6667C8.81263 22.6667 9.33496 22.1443 9.33496 21.5H4.66829Z"
        fill="#DA291C"
        mask="url(#path-1-inside-1_38925_76785)"
      />
    </svg>
  )
}

const OrderDelivered = () => {
  return (
    <svg width="28" height="29" viewBox="0 0 28 29" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.06842 2.83203L2.33301 9.93515V26.1654H25.6663V9.93515L20.9309 2.83203H20.3633H7.06842ZM19.7957 4.95324L22.624 9.19567H5.37537L8.20366 4.95324H19.7957ZM4.45422 11.3169H23.5451V24.0442H4.45422V11.3169Z"
        fill="#DA291C"
      />
      <path
        d="M20.5944 14.6764L19.25 13.332L13.877 18.7071L13.877 20.0515L15.2214 20.0515L20.5944 14.6764Z"
        fill="#DA291C"
        stroke="#DA291C"
        strokeWidth="0.466667"
      />
      <path
        d="M10.6774 16.5892L9.33303 17.9336L11.4529 20.0535L12.7973 20.0535L12.7973 18.7092L10.6774 16.5892Z"
        fill="#DA291C"
        stroke="#DA291C"
        strokeWidth="0.466667"
      />
    </svg>
  )
}

const Completed = () => {
  return (
    <svg width="28" height="29" viewBox="0 0 28 29" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="14.0007" cy="14.4987" r="10.5" stroke="#DA291C" strokeWidth="2.33333" />
      <path d="M8.43945 13.8824L12.5571 18L19.5571 11" stroke="#DA291C" strokeWidth="2.33333" />
    </svg>
  )
}

const Canceled = () => {
  return (
    <svg width="28" height="29" viewBox="0 0 28 29" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="14.0007" cy="14.4987" r="10.5" stroke="#DA291C" strokeWidth="2.33333" />
      <path
        d="M9.91602 18.5846L18.0827 10.418"
        stroke="#DA291C"
        strokeWidth="2.33333"
        strokeLinejoin="round"
      />
      <path
        d="M18.084 18.5846L9.91732 10.418"
        stroke="#DA291C"
        strokeWidth="2.33333"
        strokeLinejoin="round"
      />
    </svg>
  )
}

const Closed = () => {
  return (
    <svg width="28" height="29" viewBox="0 0 28 29" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="13.9991" cy="14.4996" r="10.6944" stroke="#DA291C" strokeWidth="2.13889" />
      <path
        d="M12.9336 18.8164V19.8486H15.0723V18.8164L14.0029 17.7832L12.9336 18.8164ZM12.9316 15.5527L14.002 16.585L15.0713 15.5527V9.17285H12.9316V15.5527Z"
        fill="#DA291C"
      />
    </svg>
  )
}

const OrderAudit = () => {
  return (
    <svg width="28" height="29" viewBox="0 0 28 29" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="14.0007" cy="14.5002" r="10.5" stroke="#DA291C" strokeWidth="2.33333" />
      <path d="M14 8.01953V14.5014H20.4815" stroke="#DA291C" strokeWidth="2.33333" />
    </svg>
  )
}

export default function Status({ type }: { type?: string | null }) {
  switch (type) {
    case 'pending':
      return <OrderPending />
    case 'processing':
      return <OrderProcessing />
    case 'shipped':
      return <OrderShipped />
    case 'delivered':
      return <OrderDelivered />
    case 'complete':
      return <Completed />
    case 'canceled':
      return <Canceled />
    case 'closed':
      return <Closed />
    case 'pending_approval':
      return <OrderAudit />
    default:
      return <Completed />
  }
}

'use client'

import dynamic from 'next/dynamic'
import { cn, type OrderDetail } from '@ninebot/core'
import { Progress } from 'antd'
import clsx from 'clsx'

import { useMediaQuery } from '@/hooks'

import { Status } from '../icons'

// 动态导入 Countdown 组件，禁用 SSR
const Countdown = dynamic(() => import('antd').then((mod) => mod.Statistic.Countdown), {
  ssr: false,
})

interface OrderProgressProps {
  endtime?: OrderDetail['payment_time_out']
  refetchOrderDetail?: () => void
  status_tab?: OrderDetail['status_tab']
  status_tab_label?: OrderDetail['status_tab_label']
  status_code?: OrderDetail['status_code']
}

export default function OrderProgress({
  endtime,
  refetchOrderDetail,
  status_tab_label = '',
  status_tab = [],
  status_code,
}: OrderProgressProps) {
  const responsive = useMediaQuery()

  // 找到最后一个is_arrived为true的元素的索引
  const lastArrivedIndex = (status_tab || []).map((step) => step?.is_arrived).lastIndexOf(true)

  return (
    <div className="flex flex-col items-center gap-base-24 pt-base-48">
      {/* 订单状态 */}
      <div className="flex flex-col items-center gap-base-12">
        <div className="flex items-center gap-4 font-miSansMedium380 text-[22px] leading-none text-primary">
          <Status type={status_code} />
          {status_tab_label}
        </div>
        {status_tab_label === '待付款' && endtime && (
          <div className="flex items-center whitespace-nowrap font-miSansDemiBold450 text-[14px] leading-[1.2]">
            <div>还剩</div>
            <Countdown
              valueStyle={{
                display: 'flex',
                alignItems: 'center',
                fontSize: '14px',
                color: '#DA291C',
                justifyContent: 'center',
                width: '70px',
              }}
              value={Number(endtime) * 1000}
              onFinish={() => {
                refetchOrderDetail?.()
              }}
            />
            <div>订单自动关闭</div>
          </div>
        )}
      </div>

      <div className="relative flex items-center gap-6 2xl:gap-8">
        {status_tab?.map((step, index) => (
          <div key={step?.status} className="flex items-center gap-6 2xl:gap-8">
            {/* 步骤点 */}
            <div
              className={clsx(
                'flex h-[34px] w-[34px] items-center justify-center rounded-full',
                index === lastArrivedIndex && step?.is_arrived ? 'border-[2px] border-primary' : '',
              )}>
              <div
                className={`z-10 flex h-[24px] w-[24px] items-center justify-center rounded-full ${step?.is_arrived ? 'bg-primary' : 'bg-gray-base'}`}>
                {step?.is_arrived && (
                  <svg
                    width="13"
                    height="9"
                    viewBox="0 0 13 9"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M12.2422 2.15232L11.0898 1L5.44145 6.65018L5.44145 7.8025L6.59378 7.8025L12.2422 2.15232Z"
                      fill="white"
                      stroke="white"
                      strokeWidth="0.4"
                    />
                    <path
                      d="M1.85154 3.9844L0.699219 5.13672L3.36396 7.80146L4.51629 7.80146L4.51628 6.64914L1.85154 3.9844Z"
                      fill="white"
                      stroke="white"
                      strokeWidth="0.4"
                    />
                  </svg>
                )}
              </div>
            </div>
            {/* 进度条连接线 - 只在非最后一个步骤时显示 */}
            {index < status_tab.length - 1 && (
              <Progress
                percent={
                  index < lastArrivedIndex
                    ? 100 // 已完成的步骤显示 100%
                    : index === lastArrivedIndex
                      ? 50 // 当前进行中的步骤显示 50%
                      : 0 // 未开始的步骤显示 0%
                }
                style={{
                  width:
                    status_tab_label === '取消' ? '300px' : responsive?.['2xl'] ? '120px' : '58px',
                }}
                size={[
                  status_tab_label === '取消' ? 300 : responsive?.['2xl'] ? '120px' : '58px',
                  8,
                ]}
                strokeColor="#DA291C"
                trailColor="#F3F3F4"
                showInfo={false}
              />
            )}
          </div>
        ))}
      </div>

      {/* 步骤状态 */}
      <div
        className={clsx(
          'flex',
          status_tab_label === '取消' ? 'gap-[266px]' : responsive?.['2xl'] ? 'gap-32' : 'gap-12',
        )}>
        {status_tab?.map((step) => (
          <div
            key={step?.status}
            className={cn('w-[94px] text-center', {
              'w-[115px]': responsive?.['2xl'],
            })}>
            <div className={`text-lg ${step?.is_arrived ? 'text-[#0f0f0f]' : 'text-gray-3'}`}>
              {step?.status}
            </div>
            {step?.time && (
              <div className={`left-0 mt-1 font-miSansRegular330 text-[14px] text-[#6E6E73]`}>
                {step.time}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}

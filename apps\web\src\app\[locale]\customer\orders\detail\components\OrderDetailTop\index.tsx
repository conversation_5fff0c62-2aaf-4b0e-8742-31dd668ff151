import { formatTimestamp } from '@ninebot/core/src/utils'

import { useOrderDetail } from '../../context/orderDetailContext'

import ActionButtons from './ActionButtons'
import OrderProgress from './OrderProgress'

export default function OrderDetailTop() {
  const { orderData, copyToClipboard, isMigrated, migrationOrder, getI18nString, refetch } =
    useOrderDetail()

  const handleCopyOrderId = () => {
    copyToClipboard(!isMigrated ? (orderData?.number ?? '') : (migrationOrder?.order_num ?? ''))
  }

  return (
    <>
      <div className="border-b border-gray-base pb-base-32">
        <span className="font-miSansDemiBold450 text-[28px] leading-[1.2]">
          {getI18nString('order_detail')}
        </span>
      </div>
      <div className="mt-base-32 flex items-center justify-between">
        <div className="flex flex-col gap-[8px] 2xl:flex-row 2xl:items-center 2xl:gap-base-32">
          <span className="font-miSansRegular330 text-[14px] leading-[1.6] text-[#444446]">
            {getI18nString('order_number')}
            {!isMigrated ? orderData?.number : migrationOrder?.order_num}

            <button onClick={handleCopyOrderId} className="ml-2 text-black">
              {getI18nString('copy')}
            </button>
          </span>
          <span className="font-miSansRegular330 text-[14px] leading-[1.6] text-[#444446]">
            {formatTimestamp(isMigrated ? migrationOrder?.create_date : orderData?.order_date)}
          </span>
        </div>

        {/* 右侧操作按钮 */}
        {!isMigrated && <ActionButtons />}
      </div>

      {!isMigrated && (
        <OrderProgress
          endtime={orderData?.payment_time_out}
          refetchOrderDetail={refetch}
          status_code={orderData?.status_code}
          status_tab={orderData?.status_tab}
          status_tab_label={orderData?.status_tab_label ?? ''}
        />
      )}
    </>
  )
}

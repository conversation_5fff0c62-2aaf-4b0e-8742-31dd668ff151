'use client'
import { useEffect, useState } from 'react'
import dynamic from 'next/dynamic'
import { useTranslations } from 'next-intl'
import { Coupon, TRACK_EVENT, useCoupons, useVolcAnalytics } from '@ninebot/core'

const CouponTabs = dynamic(() => import('../../../../components/business/user/CouponTabs'), {
  ssr: false,
})

export default function CouponsPage() {
  const getI18nString = useTranslations('Web')
  const { reportEvent } = useVolcAnalytics()
  const [activeTab, setActiveTab] = useState('0')
  const [couponList, setCouponList] = useState<{
    available: Coupon[]
    used: Coupon[]
  }>({
    available: [],
    used: [],
  })
  const [currentCoupons, setCurrentCoupons] = useState<Coupon[]>([])
  const { fetchCoupons, couponsGrouped } = useCoupons()
  const [isLoading, setIsLoading] = useState(true)

  const handleTabChange = (tab: string) => {
    setActiveTab(tab)
    setCurrentCoupons(tab === '0' ? couponList.available : couponList.used)
  }

  // 添加声明内容组件
  const CouponDeclaration = () => (
    <div className="rounded-[20px] bg-white p-base-24">
      <div className="mb-base-16">
        <span className="font-miSansMedium380 text-[20px] leading-[140%]">
          {getI18nString('coupon_declare')}
        </span>
      </div>
      <ul className="space-y-2 font-miSansRegular330 text-[14px] leading-[180%] text-gray-3">
        <li>{getI18nString('coupon_dec1')}</li>
        <li>{getI18nString('coupon_dec2')}</li>
        <li>{getI18nString('coupon_dec3')}</li>
        <li>{getI18nString('coupon_dec4')}</li>
        <li>{getI18nString('coupon_dec5')}</li>
      </ul>
    </div>
  )

  useEffect(() => {
    fetchCoupons().then((data) => {
      if (data) {
        const groupData = couponsGrouped(data as Coupon[])
        setCouponList({
          available: groupData.available,
          used: groupData.used,
        })
        if (groupData.available.length) {
          setCurrentCoupons(groupData.available)
        }
        setIsLoading(false)
      }
    })
  }, [fetchCoupons, couponsGrouped])

  /**
   * 埋点：点击我的优惠券
   */
  useEffect(() => {
    reportEvent(TRACK_EVENT.shop_coupon_page_exposure)
  }, [reportEvent])

  return (
    <div className="flex-1">
      <div className="mb-base-16 rounded-[20px] bg-white p-base-32">
        {/* 标题 */}
        <div className="mb-base-24 font-miSansDemiBold450 text-[28px] leading-[120%]">
          {getI18nString('my_coupon')}
        </div>

        {/* 标签页 */}
        <CouponTabs
          coupons={currentCoupons}
          activeTab={activeTab}
          handleTabChange={handleTabChange}
          isLoading={isLoading}
          // isCouponPage={true}
        />
      </div>

      {/* 添加声明组件 */}
      {currentCoupons.length > 0 && <CouponDeclaration />}
    </div>
  )
}

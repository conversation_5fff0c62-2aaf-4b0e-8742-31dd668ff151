import { useCallback } from 'react'
import { useTranslations } from 'next-intl'
import { type Coupon, hasDecimal, mergeStyles, NCoinView, Price, RenderHtml } from '@ninebot/core'
import { Tooltip } from 'antd'
import Big from 'big.js'

import { Sign, Tips } from './icons'

const CouponItem = ({ coupon }: { coupon: Coupon }) => {
  const { expiration_date, rule_info, status } = coupon
  const getI18nString = useTranslations('Common')
  const disabled = status === 1
  // 检查是否为 NCoin 类型
  const isNCoin = 'is_ncoin' in rule_info && rule_info.is_ncoin
  const isFixed = ['cart_fixed', 'by_fixed'].includes(rule_info.simple_action)
  const isPercent = ['by_percent'].includes(rule_info.simple_action)

  /**
   * 渲染优惠信息
   */
  const renderDiscount = useCallback(() => {
    if (Number(rule_info.discount_amount) === 0) {
      return (
        <span
          className={mergeStyles(
            'font-miSansDemiBold450 text-[20px] leading-none text-primary',
            disabled && 'text-[#6E6E73]',
          )}>
          {rule_info.label}
        </span>
      )
    }

    if (isFixed) {
      if (isNCoin) {
        return (
          <NCoinView
            number={rule_info.discount_amount}
            iconStyle={{ background: disabled ? '#6E6E73' : '#DA291C' }}
            textStyle={mergeStyles(
              'font-miSansDemiBold450 text-[20px] leading-none text-primary',
              disabled && 'text-[#6E6E73]',
            )}
          />
        )
      }

      return (
        <Price
          bold
          color="primary"
          currencyStyle={mergeStyles('text-[18px] leading-[1.2]', disabled && 'text-[#6E6E73]')}
          textStyle={mergeStyles(
            'font-miSansDemiBold450 text-[40px] leading-none',
            disabled && 'text-[#6E6E73]',
          )}
          showFraction={false}
          price={{ value: rule_info.discount_amount }}
        />
      )
    }

    if (isPercent) {
      const amount = new Big(100).minus(new Big(rule_info.discount_amount)).div(100).times(10)

      return (
        <div className="flex items-baseline">
          <span
            className={mergeStyles(
              'font-miSansDemiBold450 text-[28px] leading-[1.2] text-primary',
              disabled && 'text-[#6E6E73]',
            )}>
            {hasDecimal(amount.toNumber()) ? amount.toFixed(1) : amount.toString()}
          </span>
          <span
            className={mergeStyles(
              'text-[18px] leading-[1.2] text-primary',
              disabled && 'text-[#6E6E73]',
            )}>
            {getI18nString('coupon_code_percent')}
          </span>
        </div>
      )
    }

    return (
      <span
        className={mergeStyles(
          'text-[40px] leading-none text-primary',
          disabled && 'text-[#6E6E73]',
        )}>
        {rule_info.discount_amount}
      </span>
    )
  }, [rule_info, disabled, getI18nString, isNCoin, isFixed, isPercent])
  return (
    <div
      className={mergeStyles(
        'relative flex h-[110px] w-full items-center justify-between overflow-hidden rounded-[12px] p-[4px]',
        status === 0 ? 'bg-[#FEE5E560]' : 'bg-gray-base',
      )}>
      <div
        className={mergeStyles(
          'flex flex-1 flex-col items-center justify-center pl-[4px] pr-[8px]',
          status === 0 ? 'text-primary' : 'text-[#6E6E73]',
        )}
        style={{
          width: '24.2%',
        }}>
        {/* 优惠券金额 */}
        <div className="mb-[4px] flex items-baseline">{renderDiscount()}</div>
        {/* 使用条件 */}
        {rule_info.short_description && (
          <div
            className={mergeStyles(
              'line-clamp-2 text-center font-miSansRegular330 leading-[1.2] text-primary',
              !isNCoin ? 'text-[12px]' : 'text-[12px]',
              disabled && 'text-[#6E6E73]',
            )}>
            {rule_info.short_description}
          </div>
        )}
      </div>
      {/* 优惠券容器 - 添加relative定位以便定位半圆 */}
      <div className="relative flex h-[102px] w-[68.1%] overflow-hidden rounded-[8px] bg-white px-[20px] py-[14px] text-left">
        {/* 上侧半圆遮盖 */}
        <div
          className={mergeStyles(
            'absolute -top-[6px] right-[22.7%] h-[12px] w-[12px] rounded-full 2xl:right-[33.7%]',
            status === 0 ? 'bg-[#FEE5E560]' : 'bg-gray-base',
          )}></div>

        {/* 下侧半圆遮盖 */}
        <div
          className={mergeStyles(
            'absolute -bottom-[6px] right-[22.7%] h-[12px] w-[12px] rounded-full 2xl:right-[33.7%]',
            status === 0 ? 'bg-[#FEE5E560]' : 'bg-gray-base',
          )}></div>

        {/* 优惠券图标 */}
        <div className="absolute bottom-0 right-base-12">
          <Sign fill={status === 0 ? '#FEE5E5CC' : '#F3F3F4'} />
        </div>

        <div className="relative z-10 flex flex-col justify-between">
          {/* 优惠券标题 */}
          <h6
            className="m-0 mb-[4px] line-clamp-1 font-miSansDemiBold450 text-[16px] leading-[140%] text-[#000000]"
            title={rule_info.name}>
            {rule_info.name}
          </h6>

          {/* 有效期 */}
          <div className="flex items-center justify-between">
            {expiration_date ? (
              <div className="font-miSansRegular330 text-[12px] leading-[100%] text-[#86868B]">
                {expiration_date} {getI18nString('expires')}
              </div>
            ) : null}
            {/* <Button
              className="coupon-btn"
              type="primary"
              size="small"
              disabled={status === 1}
              onClick={onUse}>
              去使用
            </Button> */}
          </div>

          {/* 详细信息 - 使用 Tooltip */}
          {rule_info.description && (
            <div className="mt-base-16 inline-flex items-center gap-[4px]">
              <span className="font-miSansMedium380 text-[12px] leading-none text-[#86868B]">
                {getI18nString('my_coupon_detail')}
              </span>
              <Tooltip
                rootClassName="tooltip-coupon"
                title={
                  <RenderHtml contentStyle="coupon-desc-content" content={rule_info.description} />
                }
                placement="bottom"
                color="#ffffff"
                overlayInnerStyle={{
                  color: '#1F1F1F',
                  padding: '12px',
                  fontSize: '14px',
                }}>
                <div className="cursor-pointer">
                  <Tips />
                </div>
              </Tooltip>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default CouponItem
